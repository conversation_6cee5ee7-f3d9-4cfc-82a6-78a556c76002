package blob

import (
	"context"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// S3Store S3兼容存储实现（支持AWS S3、Min<PERSON>、阿里云OSS等）
// 使用单个bucket，通过目录结构区分公开和私有文件
type S3Store struct {
	client *minio.Client
	config S3Config
	bucket string
}

// NewS3Store 创建S3兼容存储实例
func NewS3Store(config S3Config) (*S3Store, error) {
	// 构建端点
	endpoint := config.Endpoint
	if endpoint == "" && config.Region != "" {
		// AWS S3默认端点
		endpoint = "s3." + config.Region + ".amazonaws.com"
	}

	// 创建客户端
	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, ""),
		Secure: config.UseSSL,
		Region: config.Region,
	})
	if err != nil {
		return nil, &Error{Code: "CLIENT_INIT_FAILED", Message: "failed to initialize S3 client", Cause: err}
	}

	store := &S3Store{
		client:        client,
		config:        config,
		privateBucket: config.Bucket,
		publicBucket:  config.PublicBucket,
	}

	// 确保桶存在
	if err := store.ensureBucketsExist(context.Background()); err != nil {
		return nil, err
	}

	return store, nil
}

// Type 返回存储类型
func (s *S3Store) Type() string {
	return "s3"
}

// Put 上传文件
func (s *S3Store) Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	bucket := s.getBucket(opts != nil && opts.Public)

	// 构建上传选项
	putOpts := minio.PutObjectOptions{}
	if opts != nil {
		if opts.ContentType != "" {
			putOpts.ContentType = opts.ContentType
		}
		if len(opts.Metadata) > 0 {
			putOpts.UserMetadata = opts.Metadata
		}
		if opts.CacheControl != "" {
			putOpts.CacheControl = opts.CacheControl
		}
	}

	// 上传文件
	_, err := s.client.PutObject(ctx, bucket, key, data, -1, putOpts)
	if err != nil {
		return &Error{Code: "UPLOAD_FAILED", Message: "failed to upload file", Cause: err}
	}

	return nil
}

// Get 下载文件
func (s *S3Store) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 先尝试公开桶
	if s.publicBucket != "" {
		if obj, err := s.client.GetObject(ctx, s.publicBucket, key, minio.GetObjectOptions{}); err == nil {
			return obj, nil
		}
	}

	// 再尝试私有桶
	obj, err := s.client.GetObject(ctx, s.privateBucket, key, minio.GetObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return nil, ErrNotFound
		}
		return nil, &Error{Code: "DOWNLOAD_FAILED", Message: "failed to download file", Cause: err}
	}

	return obj, nil
}

// Delete 删除文件
func (s *S3Store) Delete(ctx context.Context, key string) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	// 删除公开桶中的文件
	if s.publicBucket != "" {
		s.client.RemoveObject(ctx, s.publicBucket, key, minio.RemoveObjectOptions{})
	}

	// 删除私有桶中的文件
	err := s.client.RemoveObject(ctx, s.privateBucket, key, minio.RemoveObjectOptions{})
	if err != nil {
		return &Error{Code: "DELETE_FAILED", Message: "failed to delete file", Cause: err}
	}

	return nil
}

// Exists 检查文件是否存在
func (s *S3Store) Exists(ctx context.Context, key string) (bool, error) {
	if err := s.validateKey(key); err != nil {
		return false, err
	}

	// 检查公开桶
	if s.publicBucket != "" {
		if _, err := s.client.StatObject(ctx, s.publicBucket, key, minio.StatObjectOptions{}); err == nil {
			return true, nil
		}
	}

	// 检查私有桶
	_, err := s.client.StatObject(ctx, s.privateBucket, key, minio.StatObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return false, nil
		}
		return false, &Error{Code: "STAT_FAILED", Message: "failed to check file existence", Cause: err}
	}

	return true, nil
}

// GetPresignedUploadURL 获取预签名上传URL
func (s *S3Store) GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	bucket := s.getBucket(opts != nil && opts.Public)
	expires := 15 * time.Minute // 默认15分钟
	if opts != nil && opts.Expires > 0 {
		expires = opts.Expires
	}

	// 构建预签名选项
	reqParams := make(url.Values)
	if opts != nil && opts.ContentType != "" {
		reqParams.Set("Content-Type", opts.ContentType)
	}

	// 生成预签名URL
	presignedURL, err := s.client.PresignedPutObject(ctx, bucket, key, expires)
	if err != nil {
		return nil, &Error{Code: "PRESIGN_FAILED", Message: "failed to generate presigned upload URL", Cause: err}
	}

	result := &PresignedURL{
		URL:     presignedURL.String(),
		Expires: time.Now().Add(expires),
		Headers: make(map[string]string),
	}

	if opts != nil && opts.ContentType != "" {
		result.Headers["Content-Type"] = opts.ContentType
	}

	return result, nil
}

// GetPresignedDownloadURL 获取预签名下载URL
func (s *S3Store) GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 确定使用哪个桶
	bucket := s.privateBucket
	if opts != nil && opts.Public && s.publicBucket != "" {
		bucket = s.publicBucket
	}

	expires := 1 * time.Hour // 默认1小时
	if opts != nil && opts.Expires > 0 {
		expires = opts.Expires
	}

	// 生成预签名URL
	presignedURL, err := s.client.PresignedGetObject(ctx, bucket, key, expires, nil)
	if err != nil {
		return nil, &Error{Code: "PRESIGN_FAILED", Message: "failed to generate presigned download URL", Cause: err}
	}

	return &PresignedURL{
		URL:     presignedURL.String(),
		Expires: time.Now().Add(expires),
		Headers: make(map[string]string),
	}, nil
}

// GetPublicURL 获取公开访问URL
func (s *S3Store) GetPublicURL(key string) string {
	if s.publicBucket == "" {
		return ""
	}

	// 构建公开访问URL
	scheme := "http"
	if s.config.UseSSL {
		scheme = "https"
	}

	// 清理key，确保不以/开头
	key = strings.TrimPrefix(key, "/")

	endpoint := s.config.Endpoint
	if endpoint == "" && s.config.Region != "" {
		// AWS S3默认端点
		endpoint = "s3." + s.config.Region + ".amazonaws.com"
	}

	if s.config.ForcePathStyle {
		// 路径样式: https://endpoint/bucket/key
		return scheme + "://" + endpoint + "/" + s.publicBucket + "/" + key
	} else {
		// 虚拟主机样式: https://bucket.endpoint/key
		return scheme + "://" + s.publicBucket + "." + endpoint + "/" + key
	}
}

// 辅助方法

// validateKey 验证key的有效性
func (s *S3Store) validateKey(key string) error {
	if key == "" {
		return ErrInvalidKey
	}
	return nil
}

// getBucket 根据是否公开选择桶
func (s *S3Store) getBucket(isPublic bool) string {
	if isPublic && s.publicBucket != "" {
		return s.publicBucket
	}
	return s.privateBucket
}

// ensureBucketsExist 确保桶存在
func (s *S3Store) ensureBucketsExist(ctx context.Context) error {
	// 检查并创建私有桶
	if exists, err := s.client.BucketExists(ctx, s.privateBucket); err != nil {
		return &Error{Code: "BUCKET_CHECK_FAILED", Message: "failed to check private bucket", Cause: err}
	} else if !exists {
		if err := s.client.MakeBucket(ctx, s.privateBucket, minio.MakeBucketOptions{Region: s.config.Region}); err != nil {
			return &Error{Code: "BUCKET_CREATE_FAILED", Message: "failed to create private bucket", Cause: err}
		}
	}

	// 检查并创建公开桶
	if s.publicBucket != "" && s.publicBucket != s.privateBucket {
		if exists, err := s.client.BucketExists(ctx, s.publicBucket); err != nil {
			return &Error{Code: "BUCKET_CHECK_FAILED", Message: "failed to check public bucket", Cause: err}
		} else if !exists {
			if err := s.client.MakeBucket(ctx, s.publicBucket, minio.MakeBucketOptions{Region: s.config.Region}); err != nil {
				return &Error{Code: "BUCKET_CREATE_FAILED", Message: "failed to create public bucket", Cause: err}
			}

			// 设置公开桶的访问策略
			if err := s.setPublicBucketPolicy(ctx); err != nil {
				return err
			}
		}
	}

	return nil
}

// setPublicBucketPolicy 设置公开桶的访问策略
func (s *S3Store) setPublicBucketPolicy(ctx context.Context) error {
	// 设置桶策略为公开读取
	policy := `{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Principal": "*",
				"Action": "s3:GetObject",
				"Resource": "arn:aws:s3:::` + s.publicBucket + `/*"
			}
		]
	}`

	return s.client.SetBucketPolicy(ctx, s.publicBucket, policy)
}
