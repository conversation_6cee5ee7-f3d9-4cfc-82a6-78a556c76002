package blob

import (
	"context"
	"io"
	"time"
)

// Store 统一的Blob存储接口
type Store interface {
	// 基础操作
	Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error
	Get(ctx context.Context, key string) (io.ReadCloser, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
	
	// 预签名URL操作（仅S3兼容存储支持）
	GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error)
	GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error)
	
	// 公开访问URL
	GetPublicURL(key string) string
	
	// 存储类型
	Type() string
}

// PutOptions 上传选项
type PutOptions struct {
	ContentType  string            // MIME类型
	Metadata     map[string]string // 自定义元数据
	Public       bool              // 是否公开访问
	CacheControl string            // 缓存控制
}

// PresignedOptions 预签名URL选项
type PresignedOptions struct {
	Expires     time.Duration     // 过期时间
	ContentType string            // 内容类型限制
	Public      bool              // 是否公开
}

// PresignedURL 预签名URL结果
type PresignedURL struct {
	URL     string            // 预签名URL
	Headers map[string]string // 需要设置的请求头
	Expires time.Time         // 过期时间
}

// Config 存储配置
type Config struct {
	Type     string      `yaml:"type" json:"type"`         // 存储类型: local, s3
	Local    LocalConfig `yaml:"local" json:"local"`       // 本地存储配置
	S3       S3Config    `yaml:"s3" json:"s3"`             // S3兼容存储配置
	BasePath string      `yaml:"base_path" json:"base_path"` // 基础路径
}

// LocalConfig 本地存储配置
type LocalConfig struct {
	RootPath  string `yaml:"root_path" json:"root_path"`   // 根目录
	PublicURL string `yaml:"public_url" json:"public_url"` // 公开访问URL前缀
}

// S3Config S3兼容存储配置（支持AWS S3、MinIO、阿里云OSS等）
type S3Config struct {
	Endpoint        string `yaml:"endpoint" json:"endpoint"`                   // 端点URL（MinIO/OSS等）
	Region          string `yaml:"region" json:"region"`                       // 区域（AWS S3）
	Bucket          string `yaml:"bucket" json:"bucket"`                       // 私有桶名
	PublicBucket    string `yaml:"public_bucket" json:"public_bucket"`         // 公开桶名
	AccessKeyID     string `yaml:"access_key_id" json:"access_key_id"`         // 访问密钥ID
	SecretAccessKey string `yaml:"secret_access_key" json:"secret_access_key"` // 访问密钥
	UseSSL          bool   `yaml:"use_ssl" json:"use_ssl"`                     // 是否使用SSL
	ForcePathStyle  bool   `yaml:"force_path_style" json:"force_path_style"`   // 强制路径样式（MinIO需要）
}

// Error 存储错误
type Error struct {
	Code    string
	Message string
	Cause   error
}

func (e *Error) Error() string {
	if e.Cause != nil {
		return e.Message + ": " + e.Cause.Error()
	}
	return e.Message
}

// 常见错误
var (
	ErrNotFound      = &Error{Code: "NOT_FOUND", Message: "object not found"}
	ErrNotSupported  = &Error{Code: "NOT_SUPPORTED", Message: "operation not supported"}
	ErrInvalidKey    = &Error{Code: "INVALID_KEY", Message: "invalid object key"}
	ErrAccessDenied  = &Error{Code: "ACCESS_DENIED", Message: "access denied"}
)

// NewStore 创建Blob存储实例
func NewStore(config Config) (Store, error) {
	switch config.Type {
	case "local":
		return NewLocalStore(config.Local), nil
	case "s3":
		return NewS3Store(config.S3)
	default:
		return nil, &Error{Code: "UNSUPPORTED_TYPE", Message: "unsupported storage type: " + config.Type}
	}
}
