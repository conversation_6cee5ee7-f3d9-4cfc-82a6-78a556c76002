package service

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/limitcool/starter/configs"
	"github.com/limitcool/starter/internal/model"
	"github.com/limitcool/starter/internal/pkg/blobstore"
	"github.com/limitcool/starter/internal/pkg/errorx"
)

// FileService 文件服务
type FileService struct {
	blobStore blobstore.BlobStore
	fileRepo  *model.FileRepo
	config    *configs.Config
}

// NewFileService 创建文件服务
func NewFileService(blobStore blobstore.BlobStore, fileRepo *model.FileRepo, config *configs.Config) *FileService {
	return &FileService{
		blobStore: blobStore,
		fileRepo:  fileRepo,
		config:    config,
	}
}

// UploadRequest 上传请求
type UploadRequest struct {
	Filename    string `json:"filename" binding:"required"`
	ContentType string `json:"content_type"`
	FileType    string `json:"file_type" binding:"required"`
	IsPublic    bool   `json:"is_public"`
	Usage       string `json:"usage"`
}

// UploadResponse 上传响应
type UploadResponse struct {
	FileID      uint   `json:"file_id"`
	Key         string `json:"key"`
	UploadURL   string `json:"upload_url,omitempty"`
	DownloadURL string `json:"download_url,omitempty"`
	PublicURL   string `json:"public_url,omitempty"`
	ExpiresAt   int64  `json:"expires_at,omitempty"`
}

// GetUploadURL 获取上传URL
func (s *FileService) GetUploadURL(ctx context.Context, userID int64, req *UploadRequest) (*UploadResponse, error) {
	// 验证文件类型
	ext := strings.ToLower(filepath.Ext(req.Filename))
	if !s.isAllowedFileType(ext, req.FileType) {
		return nil, errorx.ErrInvalidParams.WithMsg("不支持的文件类型")
	}

	// 生成文件key
	key := s.generateFileKey(userID, req.FileType, req.Filename, req.IsPublic)

	// 创建文件记录
	fileModel := &model.File{
		OriginalName: req.Filename,
		Path:         key,
		Size:         0, // 上传时还不知道大小
		ContentType:  req.ContentType,
		FileType:     req.FileType,
		Usage:        req.Usage,
		IsPublic:     req.IsPublic,
		Status:       model.FileStatusPending,
		UploadedBy:   userID,
		UploadedByType: 2, // 用户上传
	}

	if err := s.fileRepo.Create(ctx, fileModel); err != nil {
		return nil, errorx.WrapError(err, "创建文件记录失败")
	}

	response := &UploadResponse{
		FileID: fileModel.ID,
		Key:    key,
	}

	// 根据存储类型返回不同的响应
	switch s.blobStore.Type() {
	case blobstore.StorageTypeLocal:
		// 本地存储需要通过API上传
		response.UploadURL = fmt.Sprintf("/api/v1/files/%d/upload", fileModel.ID)
	default:
		// 云存储使用预签名URL
		presignedURL, err := s.blobStore.GetPresignedUploadURL(ctx, key, &blobstore.PresignedOptions{
			Expires:     15 * time.Minute,
			ContentType: req.ContentType,
			Public:      req.IsPublic,
		})
		if err != nil {
			return nil, errorx.WrapError(err, "生成预签名上传URL失败")
		}
		response.UploadURL = presignedURL.URL
		response.ExpiresAt = presignedURL.Expires.Unix()
	}

	// 设置下载URL
	if req.IsPublic {
		response.PublicURL = s.blobStore.GetPublicURL(key)
	} else {
		// 私有文件的下载URL
		response.DownloadURL = fmt.Sprintf("/api/v1/files/%d/download", fileModel.ID)
	}

	return response, nil
}

// ConfirmUpload 确认上传完成
func (s *FileService) ConfirmUpload(ctx context.Context, fileID uint, userID int64) error {
	// 获取文件记录
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return errorx.WrapError(err, "文件不存在")
	}

	// 检查权限
	if file.UploadedBy != userID {
		return errorx.ErrForbidden.WithMsg("无权限操作此文件")
	}

	// 检查文件是否存在于存储中
	exists, err := s.blobStore.Exists(ctx, file.Path)
	if err != nil {
		return errorx.WrapError(err, "检查文件存在性失败")
	}
	if !exists {
		return errorx.ErrNotFound.WithMsg("文件未上传")
	}

	// 获取文件元数据
	metadata, err := s.blobStore.GetMetadata(ctx, file.Path)
	if err != nil {
		return errorx.WrapError(err, "获取文件元数据失败")
	}

	// 更新文件记录
	file.Size = metadata.Size
	file.Status = model.FileStatusCompleted
	if file.ContentType == "" {
		file.ContentType = metadata.ContentType
	}

	if err := s.fileRepo.Update(ctx, file); err != nil {
		return errorx.WrapError(err, "更新文件记录失败")
	}

	return nil
}

// GetDownloadURL 获取下载URL
func (s *FileService) GetDownloadURL(ctx context.Context, fileID uint, userID int64) (string, error) {
	// 获取文件记录
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return "", errorx.WrapError(err, "文件不存在")
	}

	// 检查权限
	if !file.IsPublic && file.UploadedBy != userID {
		return "", errorx.ErrForbidden.WithMsg("无权限访问此文件")
	}

	// 公开文件直接返回公开URL
	if file.IsPublic {
		return s.blobStore.GetPublicURL(file.Path), nil
	}

	// 私有文件生成预签名URL
	presignedURL, err := s.blobStore.GetPresignedDownloadURL(ctx, file.Path, &blobstore.PresignedOptions{
		Expires: 1 * time.Hour,
		Public:  false,
	})
	if err != nil {
		return "", errorx.WrapError(err, "生成下载URL失败")
	}

	return presignedURL.URL, nil
}

// DeleteFile 删除文件
func (s *FileService) DeleteFile(ctx context.Context, fileID uint, userID int64) error {
	// 获取文件记录
	file, err := s.fileRepo.GetByID(ctx, fileID)
	if err != nil {
		return errorx.WrapError(err, "文件不存在")
	}

	// 检查权限
	if file.UploadedBy != userID {
		return errorx.ErrForbidden.WithMsg("无权限删除此文件")
	}

	// 从存储中删除文件
	if err := s.blobStore.Delete(ctx, file.Path); err != nil {
		return errorx.WrapError(err, "删除存储文件失败")
	}

	// 删除文件记录
	if err := s.fileRepo.Delete(ctx, fileID); err != nil {
		return errorx.WrapError(err, "删除文件记录失败")
	}

	return nil
}

// ListFiles 列出文件
func (s *FileService) ListFiles(ctx context.Context, userID int64, page, pageSize int, fileType string) ([]*model.File, int64, error) {
	return s.fileRepo.GetByUser(ctx, userID, page, pageSize, fileType)
}

// 辅助方法

// generateFileKey 生成文件key
func (s *FileService) generateFileKey(userID int64, fileType, filename string, isPublic bool) string {
	ext := filepath.Ext(filename)
	timestamp := time.Now().UnixNano()
	
	// 构建路径: {basePath}/{fileType}/{year}/{month}/{day}/{userID}_{timestamp}{ext}
	now := time.Now()
	datePath := fmt.Sprintf("%d/%02d/%02d", now.Year(), now.Month(), now.Day())
	fileName := fmt.Sprintf("%d_%d%s", userID, timestamp, ext)
	
	var basePath string
	if s.config.Storage.BasePath != "" {
		basePath = s.config.Storage.BasePath
	} else {
		basePath = "files"
	}
	
	return fmt.Sprintf("%s/%s/%s/%s", basePath, fileType, datePath, fileName)
}

// isAllowedFileType 检查文件类型是否允许
func (s *FileService) isAllowedFileType(ext, fileType string) bool {
	ext = strings.ToLower(ext)
	
	switch fileType {
	case model.FileTypeImage:
		return s.isImageExt(ext)
	case model.FileTypeDocument:
		return s.isDocumentExt(ext)
	case model.FileTypeVideo:
		return s.isVideoExt(ext)
	case model.FileTypeAudio:
		return s.isAudioExt(ext)
	default:
		return true
	}
}

// isImageExt 检查是否为图片扩展名
func (s *FileService) isImageExt(ext string) bool {
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	for _, allowedExt := range imageExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// isDocumentExt 检查是否为文档扩展名
func (s *FileService) isDocumentExt(ext string) bool {
	docExts := []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf"}
	for _, allowedExt := range docExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// isVideoExt 检查是否为视频扩展名
func (s *FileService) isVideoExt(ext string) bool {
	videoExts := []string{".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv"}
	for _, allowedExt := range videoExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}

// isAudioExt 检查是否为音频扩展名
func (s *FileService) isAudioExt(ext string) bool {
	audioExts := []string{".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma"}
	for _, allowedExt := range audioExts {
		if ext == allowedExt {
			return true
		}
	}
	return false
}
