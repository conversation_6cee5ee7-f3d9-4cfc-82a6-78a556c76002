package blobstore

import (
	"context"
	"io"
	"time"
)

// BlobStore 统一的Blob存储接口
type BlobStore interface {
	// 基础操作
	Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error
	Get(ctx context.Context, key string) (io.ReadCloser, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)

	// 预签名URL操作
	GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error)
	GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error)

	// 公开访问URL
	GetPublicURL(key string) string

	// 元数据操作
	GetMetadata(ctx context.Context, key string) (*ObjectMetadata, error)
	SetMetadata(ctx context.Context, key string, metadata map[string]string) error

	// 批量操作
	List(ctx context.Context, prefix string, opts *ListOptions) (*ListResult, error)

	// 存储类型
	Type() StorageType
}

// StorageType 存储类型
type StorageType string

const (
	StorageTypeLocal StorageType = "local" // 本地文件系统
	StorageTypeS3    StorageType = "s3"    // S3兼容存储(包括MinIO、AWS S3、阿里云OSS等)
)

// PutOptions 上传选项
type PutOptions struct {
	ContentType   string            // MIME类型
	ContentLength int64             // 内容长度
	Metadata      map[string]string // 自定义元数据
	Public        bool              // 是否公开访问
	CacheControl  string            // 缓存控制
}

// PresignedOptions 预签名URL选项
type PresignedOptions struct {
	Expires     time.Duration     // 过期时间
	ContentType string            // 内容类型限制
	Metadata    map[string]string // 元数据
	Public      bool              // 是否公开
}

// PresignedURL 预签名URL结果
type PresignedURL struct {
	URL     string            // 预签名URL
	Headers map[string]string // 需要设置的请求头
	Expires time.Time         // 过期时间
}

// ObjectMetadata 对象元数据
type ObjectMetadata struct {
	Key          string            // 对象键
	Size         int64             // 文件大小
	ContentType  string            // MIME类型
	LastModified time.Time         // 最后修改时间
	ETag         string            // ETag
	Metadata     map[string]string // 自定义元数据
	Public       bool              // 是否公开
}

// ListOptions 列表选项
type ListOptions struct {
	MaxKeys   int    // 最大返回数量
	Marker    string // 分页标记
	Delimiter string // 分隔符
}

// ListResult 列表结果
type ListResult struct {
	Objects        []*ObjectMetadata // 对象列表
	NextMarker     string            // 下一页标记
	IsTruncated    bool              // 是否还有更多
	CommonPrefixes []string          // 公共前缀
}

// Config 存储配置
type Config struct {
	Type     StorageType `yaml:"type" json:"type"`           // 存储类型
	Local    LocalConfig `yaml:"local" json:"local"`         // 本地存储配置
	S3       S3Config    `yaml:"s3" json:"s3"`               // S3配置
	MinIO    MinIOConfig `yaml:"minio" json:"minio"`         // MinIO配置
	OSS      OSSConfig   `yaml:"oss" json:"oss"`             // OSS配置
	BasePath string      `yaml:"base_path" json:"base_path"` // 基础路径
}

// LocalConfig 本地存储配置
type LocalConfig struct {
	RootPath  string `yaml:"root_path" json:"root_path"`   // 根目录
	PublicURL string `yaml:"public_url" json:"public_url"` // 公开访问URL前缀
	ServeMode string `yaml:"serve_mode" json:"serve_mode"` // 服务模式: direct, proxy
}

// S3Config S3存储配置
type S3Config struct {
	Region          string `yaml:"region" json:"region"`                       // 区域
	Bucket          string `yaml:"bucket" json:"bucket"`                       // 桶名
	AccessKeyID     string `yaml:"access_key_id" json:"access_key_id"`         // 访问密钥ID
	SecretAccessKey string `yaml:"secret_access_key" json:"secret_access_key"` // 访问密钥
	Endpoint        string `yaml:"endpoint" json:"endpoint"`                   // 端点URL
	UseSSL          bool   `yaml:"use_ssl" json:"use_ssl"`                     // 是否使用SSL
	PublicBucket    string `yaml:"public_bucket" json:"public_bucket"`         // 公开文件桶
}

// MinIOConfig MinIO存储配置
type MinIOConfig struct {
	Endpoint        string `yaml:"endpoint" json:"endpoint"`                   // MinIO端点
	AccessKeyID     string `yaml:"access_key_id" json:"access_key_id"`         // 访问密钥ID
	SecretAccessKey string `yaml:"secret_access_key" json:"secret_access_key"` // 访问密钥
	UseSSL          bool   `yaml:"use_ssl" json:"use_ssl"`                     // 是否使用SSL
	Bucket          string `yaml:"bucket" json:"bucket"`                       // 私有桶
	PublicBucket    string `yaml:"public_bucket" json:"public_bucket"`         // 公开桶
}

// OSSConfig 阿里云OSS配置
type OSSConfig struct {
	Endpoint        string `yaml:"endpoint" json:"endpoint"`                   // OSS端点
	AccessKeyID     string `yaml:"access_key_id" json:"access_key_id"`         // 访问密钥ID
	AccessKeySecret string `yaml:"access_key_secret" json:"access_key_secret"` // 访问密钥
	Bucket          string `yaml:"bucket" json:"bucket"`                       // 私有桶
	PublicBucket    string `yaml:"public_bucket" json:"public_bucket"`         // 公开桶
}

// Error 存储错误
type Error struct {
	Code    string
	Message string
	Cause   error
}

func (e *Error) Error() string {
	if e.Cause != nil {
		return e.Message + ": " + e.Cause.Error()
	}
	return e.Message
}

// 常见错误
var (
	ErrNotFound      = &Error{Code: "NOT_FOUND", Message: "object not found"}
	ErrAlreadyExists = &Error{Code: "ALREADY_EXISTS", Message: "object already exists"}
	ErrInvalidKey    = &Error{Code: "INVALID_KEY", Message: "invalid object key"}
	ErrAccessDenied  = &Error{Code: "ACCESS_DENIED", Message: "access denied"}
)

// NewBlobStore 创建Blob存储实例
func NewBlobStore(config Config) (BlobStore, error) {
	switch config.Type {
	case StorageTypeLocal:
		return NewLocalStore(config.Local), nil
	case StorageTypeMinIO:
		return NewMinIOStore(config.MinIO)
	case StorageTypeS3:
		// S3和MinIO使用相同的实现，只是配置不同
		minioConfig := MinIOConfig{
			Endpoint:        config.S3.Endpoint,
			AccessKeyID:     config.S3.AccessKeyID,
			SecretAccessKey: config.S3.SecretAccessKey,
			UseSSL:          config.S3.UseSSL,
			Bucket:          config.S3.Bucket,
			PublicBucket:    config.S3.PublicBucket,
		}
		return NewMinIOStore(minioConfig)
	default:
		return nil, &Error{Code: "UNSUPPORTED_TYPE", Message: "unsupported storage type: " + string(config.Type)}
	}
}
