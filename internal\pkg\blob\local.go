package blob

import (
	"context"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/limitcool/starter/internal/pkg/errorx"
)

// LocalStore 本地文件存储实现
type LocalStore struct {
	config LocalConfig
}

// NewLocalStore 创建本地存储实例
func NewLocalStore(config LocalConfig) *LocalStore {
	return &LocalStore{
		config: config,
	}
}

// Type 返回存储类型
func (s *LocalStore) Type() string {
	return "local"
}

// Put 上传文件
func (s *LocalStore) Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	filePath := s.getFilePath(key, opts != nil && opts.Public)

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return errorx.WrapError(err, "创建目录失败")
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return errorx.WrapError(err, "创建文件失败")
	}
	defer file.Close()

	// 复制数据
	_, err = io.Copy(file, data)
	if err != nil {
		return errorx.WrapError(err, "写入文件失败")
	}

	return nil
}

// Get 下载文件
func (s *LocalStore) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 先尝试公开文件
	filePath := s.getFilePath(key, true)
	if file, err := os.Open(filePath); err == nil {
		return file, nil
	}

	// 再尝试私有文件
	filePath = s.getFilePath(key, false)
	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, errorx.ErrNotFound.WithMsg("文件不存在")
		}
		return nil, errorx.WrapError(err, "读取文件失败")
	}

	return file, nil
}

// Delete 删除文件
func (s *LocalStore) Delete(ctx context.Context, key string) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	// 删除公开和私有文件
	publicPath := s.getFilePath(key, true)
	privatePath := s.getFilePath(key, false)

	var lastErr error

	if err := os.Remove(publicPath); err != nil && !os.IsNotExist(err) {
		lastErr = err
	}

	if err := os.Remove(privatePath); err != nil && !os.IsNotExist(err) {
		lastErr = err
	}

	if lastErr != nil {
		return errorx.WrapError(lastErr, "删除文件失败")
	}

	return nil
}

// Exists 检查文件是否存在
func (s *LocalStore) Exists(ctx context.Context, key string) (bool, error) {
	if err := s.validateKey(key); err != nil {
		return false, err
	}

	// 检查公开文件
	if _, err := os.Stat(s.getFilePath(key, true)); err == nil {
		return true, nil
	}

	// 检查私有文件
	if _, err := os.Stat(s.getFilePath(key, false)); err == nil {
		return true, nil
	}

	return false, nil
}

// GetPresignedUploadURL 获取预签名上传URL（本地存储不支持）
func (s *LocalStore) GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	return nil, errorx.ErrInvalidParams.WithMsg("本地存储不支持预签名上传URL")
}

// GetPresignedDownloadURL 获取预签名下载URL（本地存储不支持）
func (s *LocalStore) GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	return nil, errorx.ErrInvalidParams.WithMsg("本地存储不支持预签名下载URL")
}

// GetPublicURL 获取公开访问URL
func (s *LocalStore) GetPublicURL(key string) string {
	if s.config.PublicURL == "" {
		return ""
	}

	// 确保key以/开头
	if !strings.HasPrefix(key, "/") {
		key = "/" + key
	}

	// 确保PublicURL不以/结尾
	publicURL := strings.TrimSuffix(s.config.PublicURL, "/")

	return publicURL + "/public" + key
}

// 辅助方法

// validateKey 验证key的有效性
func (s *LocalStore) validateKey(key string) error {
	if key == "" {
		return errorx.ErrInvalidParams.WithMsg("文件key不能为空")
	}

	// 防止路径遍历攻击
	if strings.Contains(key, "..") {
		return errorx.ErrInvalidParams.WithMsg("文件key包含非法字符")
	}

	return nil
}

// getFilePath 获取文件的完整路径
func (s *LocalStore) getFilePath(key string, isPublic bool) string {
	var subDir string
	if isPublic {
		subDir = "public"
	} else {
		subDir = "private"
	}

	// 清理key，移除开头的/
	key = strings.TrimPrefix(key, "/")

	return filepath.Join(s.config.RootPath, subDir, key)
}
