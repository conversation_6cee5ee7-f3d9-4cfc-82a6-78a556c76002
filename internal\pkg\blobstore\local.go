package blobstore

import (
	"context"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// LocalStore 本地文件存储实现
type LocalStore struct {
	config LocalConfig
}

// NewLocalStore 创建本地存储实例
func NewLocalStore(config LocalConfig) *LocalStore {
	return &LocalStore{
		config: config,
	}
}

// Type 返回存储类型
func (s *LocalStore) Type() StorageType {
	return StorageTypeLocal
}

// Put 上传文件
func (s *LocalStore) Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	filePath := s.getFilePath(key, opts != nil && opts.Public)

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return &Error{Code: "CREATE_DIR_FAILED", Message: "failed to create directory", Cause: err}
	}

	// 创建文件
	file, err := os.Create(filePath)
	if err != nil {
		return &Error{Code: "CREATE_FILE_FAILED", Message: "failed to create file", Cause: err}
	}
	defer file.Close()

	// 复制数据
	_, err = io.Copy(file, data)
	if err != nil {
		return &Error{Code: "WRITE_FILE_FAILED", Message: "failed to write file", Cause: err}
	}

	// 保存元数据
	if opts != nil && len(opts.Metadata) > 0 {
		if err := s.saveMetadata(key, opts.Metadata, opts.Public); err != nil {
			// 元数据保存失败不影响文件上传
			// 可以记录日志
		}
	}

	return nil
}

// Get 下载文件
func (s *LocalStore) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 先尝试公开文件
	filePath := s.getFilePath(key, true)
	if file, err := os.Open(filePath); err == nil {
		return file, nil
	}

	// 再尝试私有文件
	filePath = s.getFilePath(key, false)
	file, err := os.Open(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, ErrNotFound
		}
		return nil, &Error{Code: "READ_FILE_FAILED", Message: "failed to read file", Cause: err}
	}

	return file, nil
}

// Delete 删除文件
func (s *LocalStore) Delete(ctx context.Context, key string) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	// 删除公开和私有文件
	publicPath := s.getFilePath(key, true)
	privatePath := s.getFilePath(key, false)

	var lastErr error

	if err := os.Remove(publicPath); err != nil && !os.IsNotExist(err) {
		lastErr = err
	}

	if err := os.Remove(privatePath); err != nil && !os.IsNotExist(err) {
		lastErr = err
	}

	// 删除元数据
	s.deleteMetadata(key, true)
	s.deleteMetadata(key, false)

	if lastErr != nil {
		return &Error{Code: "DELETE_FILE_FAILED", Message: "failed to delete file", Cause: lastErr}
	}

	return nil
}

// Exists 检查文件是否存在
func (s *LocalStore) Exists(ctx context.Context, key string) (bool, error) {
	if err := s.validateKey(key); err != nil {
		return false, err
	}

	// 检查公开文件
	if _, err := os.Stat(s.getFilePath(key, true)); err == nil {
		return true, nil
	}

	// 检查私有文件
	if _, err := os.Stat(s.getFilePath(key, false)); err == nil {
		return true, nil
	}

	return false, nil
}

// GetPresignedUploadURL 获取预签名上传URL（本地存储不支持，返回错误）
func (s *LocalStore) GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	return nil, &Error{Code: "NOT_SUPPORTED", Message: "presigned upload URL not supported for local storage"}
}

// GetPresignedDownloadURL 获取预签名下载URL（本地存储不支持，返回错误）
func (s *LocalStore) GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	return nil, &Error{Code: "NOT_SUPPORTED", Message: "presigned download URL not supported for local storage"}
}

// GetPublicURL 获取公开访问URL
func (s *LocalStore) GetPublicURL(key string) string {
	if s.config.PublicURL == "" {
		return ""
	}

	// 确保key以/开头
	if !strings.HasPrefix(key, "/") {
		key = "/" + key
	}

	// 确保PublicURL不以/结尾
	publicURL := strings.TrimSuffix(s.config.PublicURL, "/")

	return publicURL + "/public" + key
}

// GetMetadata 获取文件元数据
func (s *LocalStore) GetMetadata(ctx context.Context, key string) (*ObjectMetadata, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 检查文件是否存在并获取基本信息
	var isPublic bool

	publicPath := s.getFilePath(key, true)
	privatePath := s.getFilePath(key, false)

	if stat, err := os.Stat(publicPath); err == nil {
		isPublic = true

		metadata := &ObjectMetadata{
			Key:          key,
			Size:         stat.Size(),
			LastModified: stat.ModTime(),
			Public:       isPublic,
		}

		// 加载自定义元数据
		if customMeta := s.loadMetadata(key, isPublic); customMeta != nil {
			metadata.Metadata = customMeta
		}

		return metadata, nil
	}

	if stat, err := os.Stat(privatePath); err == nil {
		isPublic = false

		metadata := &ObjectMetadata{
			Key:          key,
			Size:         stat.Size(),
			LastModified: stat.ModTime(),
			Public:       isPublic,
		}

		// 加载自定义元数据
		if customMeta := s.loadMetadata(key, isPublic); customMeta != nil {
			metadata.Metadata = customMeta
		}

		return metadata, nil
	}

	return nil, ErrNotFound
}

// SetMetadata 设置文件元数据
func (s *LocalStore) SetMetadata(ctx context.Context, key string, metadata map[string]string) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	// 检查文件是否存在
	exists, err := s.Exists(ctx, key)
	if err != nil {
		return err
	}
	if !exists {
		return ErrNotFound
	}

	// 确定文件是公开还是私有
	isPublic := false
	if _, err := os.Stat(s.getFilePath(key, true)); err == nil {
		isPublic = true
	}

	return s.saveMetadata(key, metadata, isPublic)
}

// List 列出文件
func (s *LocalStore) List(ctx context.Context, prefix string, opts *ListOptions) (*ListResult, error) {
	// 本地存储的简单实现，实际项目中可能需要更复杂的逻辑
	return &ListResult{
		Objects:     []*ObjectMetadata{},
		IsTruncated: false,
	}, nil
}

// 辅助方法

// validateKey 验证key的有效性
func (s *LocalStore) validateKey(key string) error {
	if key == "" {
		return ErrInvalidKey
	}

	// 防止路径遍历攻击
	if strings.Contains(key, "..") {
		return ErrInvalidKey
	}

	return nil
}

// getFilePath 获取文件的完整路径
func (s *LocalStore) getFilePath(key string, isPublic bool) string {
	var subDir string
	if isPublic {
		subDir = "public"
	} else {
		subDir = "private"
	}

	// 清理key，移除开头的/
	key = strings.TrimPrefix(key, "/")

	return filepath.Join(s.config.RootPath, subDir, key)
}

// getMetadataPath 获取元数据文件路径
func (s *LocalStore) getMetadataPath(key string, isPublic bool) string {
	filePath := s.getFilePath(key, isPublic)
	return filePath + ".meta"
}

// saveMetadata 保存元数据
func (s *LocalStore) saveMetadata(key string, metadata map[string]string, isPublic bool) error {
	// 简单实现：将元数据保存为JSON文件
	// 实际项目中可能需要更复杂的实现
	return nil
}

// loadMetadata 加载元数据
func (s *LocalStore) loadMetadata(key string, isPublic bool) map[string]string {
	// 简单实现：从JSON文件加载元数据
	// 实际项目中可能需要更复杂的实现
	return nil
}

// deleteMetadata 删除元数据
func (s *LocalStore) deleteMetadata(key string, isPublic bool) error {
	metaPath := s.getMetadataPath(key, isPublic)
	err := os.Remove(metaPath)
	if os.IsNotExist(err) {
		return nil
	}
	return err
}
