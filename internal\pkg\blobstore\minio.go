package blobstore

import (
	"context"
	"io"
	"net/url"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinIOStore MinIO存储实现
type MinIOStore struct {
	client        *minio.Client
	config        MinIOConfig
	privateBucket string
	publicBucket  string
}

// NewMinIOStore 创建MinIO存储实例
func NewMinIOStore(config MinIOConfig) (*MinIOStore, error) {
	// 创建MinIO客户端
	client, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKeyID, config.SecretAccessKey, ""),
		Secure: config.UseSSL,
	})
	if err != nil {
		return nil, &Error{Code: "CLIENT_INIT_FAILED", Message: "failed to initialize MinIO client", Cause: err}
	}

	store := &MinIOStore{
		client:        client,
		config:        config,
		privateBucket: config.Bucket,
		publicBucket:  config.PublicBucket,
	}

	// 确保桶存在
	if err := store.ensureBucketsExist(context.Background()); err != nil {
		return nil, err
	}

	return store, nil
}

// Type 返回存储类型
func (s *MinIOStore) Type() StorageType {
	return StorageTypeMinIO
}

// Put 上传文件
func (s *MinIOStore) Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	bucket := s.getBucket(opts != nil && opts.Public)

	// 构建上传选项
	putOpts := minio.PutObjectOptions{}
	if opts != nil {
		if opts.ContentType != "" {
			putOpts.ContentType = opts.ContentType
		}
		if len(opts.Metadata) > 0 {
			putOpts.UserMetadata = opts.Metadata
		}
		if opts.CacheControl != "" {
			putOpts.CacheControl = opts.CacheControl
		}
	}

	// 上传文件
	_, err := s.client.PutObject(ctx, bucket, key, data, -1, putOpts)
	if err != nil {
		return &Error{Code: "UPLOAD_FAILED", Message: "failed to upload file", Cause: err}
	}

	return nil
}

// Get 下载文件
func (s *MinIOStore) Get(ctx context.Context, key string) (io.ReadCloser, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 先尝试公开桶
	if s.publicBucket != "" {
		if obj, err := s.client.GetObject(ctx, s.publicBucket, key, minio.GetObjectOptions{}); err == nil {
			return obj, nil
		}
	}

	// 再尝试私有桶
	obj, err := s.client.GetObject(ctx, s.privateBucket, key, minio.GetObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return nil, ErrNotFound
		}
		return nil, &Error{Code: "DOWNLOAD_FAILED", Message: "failed to download file", Cause: err}
	}

	return obj, nil
}

// Delete 删除文件
func (s *MinIOStore) Delete(ctx context.Context, key string) error {
	if err := s.validateKey(key); err != nil {
		return err
	}

	// 删除公开桶中的文件
	if s.publicBucket != "" {
		s.client.RemoveObject(ctx, s.publicBucket, key, minio.RemoveObjectOptions{})
	}

	// 删除私有桶中的文件
	err := s.client.RemoveObject(ctx, s.privateBucket, key, minio.RemoveObjectOptions{})
	if err != nil {
		return &Error{Code: "DELETE_FAILED", Message: "failed to delete file", Cause: err}
	}

	return nil
}

// Exists 检查文件是否存在
func (s *MinIOStore) Exists(ctx context.Context, key string) (bool, error) {
	if err := s.validateKey(key); err != nil {
		return false, err
	}

	// 检查公开桶
	if s.publicBucket != "" {
		if _, err := s.client.StatObject(ctx, s.publicBucket, key, minio.StatObjectOptions{}); err == nil {
			return true, nil
		}
	}

	// 检查私有桶
	_, err := s.client.StatObject(ctx, s.privateBucket, key, minio.StatObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return false, nil
		}
		return false, &Error{Code: "STAT_FAILED", Message: "failed to check file existence", Cause: err}
	}

	return true, nil
}

// GetPresignedUploadURL 获取预签名上传URL
func (s *MinIOStore) GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	bucket := s.getBucket(opts != nil && opts.Public)
	expires := 15 * time.Minute // 默认15分钟
	if opts != nil && opts.Expires > 0 {
		expires = opts.Expires
	}

	// 构建预签名选项
	reqParams := make(url.Values)
	if opts != nil && opts.ContentType != "" {
		reqParams.Set("Content-Type", opts.ContentType)
	}

	// 生成预签名URL
	presignedURL, err := s.client.PresignedPutObject(ctx, bucket, key, expires)
	if err != nil {
		return nil, &Error{Code: "PRESIGN_FAILED", Message: "failed to generate presigned upload URL", Cause: err}
	}

	result := &PresignedURL{
		URL:     presignedURL.String(),
		Expires: time.Now().Add(expires),
		Headers: make(map[string]string),
	}

	if opts != nil && opts.ContentType != "" {
		result.Headers["Content-Type"] = opts.ContentType
	}

	return result, nil
}

// GetPresignedDownloadURL 获取预签名下载URL
func (s *MinIOStore) GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 确定使用哪个桶
	bucket := s.privateBucket
	if opts != nil && opts.Public && s.publicBucket != "" {
		bucket = s.publicBucket
	}

	expires := 1 * time.Hour // 默认1小时
	if opts != nil && opts.Expires > 0 {
		expires = opts.Expires
	}

	// 生成预签名URL
	presignedURL, err := s.client.PresignedGetObject(ctx, bucket, key, expires, nil)
	if err != nil {
		return nil, &Error{Code: "PRESIGN_FAILED", Message: "failed to generate presigned download URL", Cause: err}
	}

	return &PresignedURL{
		URL:     presignedURL.String(),
		Expires: time.Now().Add(expires),
		Headers: make(map[string]string),
	}, nil
}

// GetPublicURL 获取公开访问URL
func (s *MinIOStore) GetPublicURL(key string) string {
	if s.publicBucket == "" {
		return ""
	}

	// 构建公开访问URL
	scheme := "http"
	if s.config.UseSSL {
		scheme = "https"
	}

	// 清理key，确保不以/开头
	key = strings.TrimPrefix(key, "/")

	return scheme + "://" + s.config.Endpoint + "/" + s.publicBucket + "/" + key
}

// GetMetadata 获取文件元数据
func (s *MinIOStore) GetMetadata(ctx context.Context, key string) (*ObjectMetadata, error) {
	if err := s.validateKey(key); err != nil {
		return nil, err
	}

	// 先尝试公开桶
	if s.publicBucket != "" {
		if stat, err := s.client.StatObject(ctx, s.publicBucket, key, minio.StatObjectOptions{}); err == nil {
			return &ObjectMetadata{
				Key:          key,
				Size:         stat.Size,
				ContentType:  stat.ContentType,
				LastModified: stat.LastModified,
				ETag:         stat.ETag,
				Metadata:     stat.UserMetadata,
				Public:       true,
			}, nil
		}
	}

	// 再尝试私有桶
	stat, err := s.client.StatObject(ctx, s.privateBucket, key, minio.StatObjectOptions{})
	if err != nil {
		if minio.ToErrorResponse(err).Code == "NoSuchKey" {
			return nil, ErrNotFound
		}
		return nil, &Error{Code: "STAT_FAILED", Message: "failed to get file metadata", Cause: err}
	}

	return &ObjectMetadata{
		Key:          key,
		Size:         stat.Size,
		ContentType:  stat.ContentType,
		LastModified: stat.LastModified,
		ETag:         stat.ETag,
		Metadata:     stat.UserMetadata,
		Public:       false,
	}, nil
}

// SetMetadata 设置文件元数据
func (s *MinIOStore) SetMetadata(ctx context.Context, key string, metadata map[string]string) error {
	// MinIO不支持直接修改元数据，需要复制对象
	return &Error{Code: "NOT_SUPPORTED", Message: "metadata modification not supported, use copy operation instead"}
}

// List 列出文件
func (s *MinIOStore) List(ctx context.Context, prefix string, opts *ListOptions) (*ListResult, error) {
	maxKeys := 1000
	if opts != nil && opts.MaxKeys > 0 {
		maxKeys = opts.MaxKeys
	}

	listOpts := minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
		MaxKeys:   maxKeys,
	}

	if opts != nil && opts.Marker != "" {
		listOpts.StartAfter = opts.Marker
	}

	// 列出私有桶中的对象
	objectCh := s.client.ListObjects(ctx, s.privateBucket, listOpts)

	var objects []*ObjectMetadata
	var nextMarker string
	var isTruncated bool

	for object := range objectCh {
		if object.Err != nil {
			return nil, &Error{Code: "LIST_FAILED", Message: "failed to list objects", Cause: object.Err}
		}

		objects = append(objects, &ObjectMetadata{
			Key:          object.Key,
			Size:         object.Size,
			LastModified: object.LastModified,
			ETag:         object.ETag,
			Public:       false,
		})

		nextMarker = object.Key
	}

	// 检查是否还有更多对象
	if len(objects) == maxKeys {
		isTruncated = true
	}

	return &ListResult{
		Objects:     objects,
		NextMarker:  nextMarker,
		IsTruncated: isTruncated,
	}, nil
}

// 辅助方法

// validateKey 验证key的有效性
func (s *MinIOStore) validateKey(key string) error {
	if key == "" {
		return ErrInvalidKey
	}
	return nil
}

// getBucket 根据是否公开选择桶
func (s *MinIOStore) getBucket(isPublic bool) string {
	if isPublic && s.publicBucket != "" {
		return s.publicBucket
	}
	return s.privateBucket
}

// ensureBucketsExist 确保桶存在
func (s *MinIOStore) ensureBucketsExist(ctx context.Context) error {
	// 检查并创建私有桶
	if exists, err := s.client.BucketExists(ctx, s.privateBucket); err != nil {
		return &Error{Code: "BUCKET_CHECK_FAILED", Message: "failed to check private bucket", Cause: err}
	} else if !exists {
		if err := s.client.MakeBucket(ctx, s.privateBucket, minio.MakeBucketOptions{}); err != nil {
			return &Error{Code: "BUCKET_CREATE_FAILED", Message: "failed to create private bucket", Cause: err}
		}
	}

	// 检查并创建公开桶
	if s.publicBucket != "" && s.publicBucket != s.privateBucket {
		if exists, err := s.client.BucketExists(ctx, s.publicBucket); err != nil {
			return &Error{Code: "BUCKET_CHECK_FAILED", Message: "failed to check public bucket", Cause: err}
		} else if !exists {
			if err := s.client.MakeBucket(ctx, s.publicBucket, minio.MakeBucketOptions{}); err != nil {
				return &Error{Code: "BUCKET_CREATE_FAILED", Message: "failed to create public bucket", Cause: err}
			}

			// 设置公开桶的访问策略
			if err := s.setPublicBucketPolicy(ctx); err != nil {
				return err
			}
		}
	}

	return nil
}

// setPublicBucketPolicy 设置公开桶的访问策略
func (s *MinIOStore) setPublicBucketPolicy(ctx context.Context) error {
	// 设置桶策略为公开读取
	policy := `{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Principal": "*",
				"Action": "s3:GetObject",
				"Resource": "arn:aws:s3:::` + s.publicBucket + `/*"
			}
		]
	}`

	return s.client.SetBucketPolicy(ctx, s.publicBucket, policy)
}
