APP:
  Name: MyApp
  Port: 8080
JwtAuth:
  AccessSecret: uOvKLmVfztaXGpNYd4Z0I1SiT7MweJhl
  AccessExpire: 2592000
  RefreshSecret: uOvKLmVfztaXGpNYd4Z0I1SiT7MweJhl
  RefreshExpire: 2592000
Driver: sqlite3
Database:
  Enabled: true
  UserName: root
  Password:
  DBName: app
  Host: localhost
  Port: 3306
  TablePrefix:
  Charset: utf8mb4
  ParseTime: true
  Loc: Asia%2FShanghai
Mongo:
  Enabled: false
  URI: mongodb://localhost:27017
  User:
  Password:
  DB: myapp
Redis:
  "default":
    Enabled: false
    Addr: localhost:6379
    Password:
    DB: 0
    MinIdleConn: 200
    DialTimeout: 60s
    ReadTimeout: 5000ms
    WriteTimeout: 5000ms
    PoolSize: 100
    PoolTimeout: 240s
    EnableTrace: true
Casbin:
  Enabled: true
  ModelPath: configs/rbac_model.conf
  PolicyTable: casbin_rule
  AutoLoadInterval: 30
Log:
  Level: debug
  Output: ["console"]
  Format: text
  FileConfig:
    Path: logs/app.log
    MaxSize: 10
  StackTraceEnabled: true
  StackTraceLevel: error
  MaxStackFrames: 10
Storage:
  Enabled: true
  Type: local  # 可选: local, minio, s3, oss
  BasePath: files
  Local:
    RootPath: storage
    PublicURL: http://localhost:8080/static
    ServeMode: direct  # direct: 直接访问, proxy: 代理访问
  MinIO:
    Endpoint: localhost:9000
    AccessKeyID: minioadmin
    SecretAccessKey: minioadmin
    UseSSL: false
    Bucket: private-files
    PublicBucket: public-files
  S3:
    Region: us-east-1
    Bucket: my-private-bucket
    PublicBucket: my-public-bucket
    AccessKeyID: ""
    SecretAccessKey: ""
    Endpoint: ""  # 留空使用AWS S3，或填入自定义端点
    UseSSL: true
Admin:
  Username: admin
  Password: admin123
  Nickname: 系统管理员
I18n:
  Enabled: true
  DefaultLanguage: zh-CN
  SupportLanguages:
    - zh-CN
    - en-US
  ResourcesPath: locales

# 性能分析配置
Pprof:
  Enabled: false  # 是否启用pprof，生产环境建议设为false
  Port: 0         # pprof服务端口，0表示使用主服务端口，也可以设置独立端口如6060