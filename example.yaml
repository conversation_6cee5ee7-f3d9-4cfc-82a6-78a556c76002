APP:
  Name: MyApp
  Port: 8080
JwtAuth:
  AccessSecret: uOvKLmVfztaXGpNYd4Z0I1SiT7MweJhl
  AccessExpire: 2592000
  RefreshSecret: uOvKLmVfztaXGpNYd4Z0I1SiT7MweJhl
  RefreshExpire: 2592000
Driver: sqlite3
Database:
  Enabled: true
  UserName: root
  Password:
  DBName: app
  Host: localhost
  Port: 3306
  TablePrefix:
  Charset: utf8mb4
  ParseTime: true
  Loc: Asia%2FShanghai
Mongo:
  Enabled: false
  URI: mongodb://localhost:27017
  User:
  Password:
  DB: myapp
Redis:
  "default":
    Enabled: false
    Addr: localhost:6379
    Password:
    DB: 0
    MinIdleConn: 200
    DialTimeout: 60s
    ReadTimeout: 5000ms
    WriteTimeout: 5000ms
    PoolSize: 100
    PoolTimeout: 240s
    EnableTrace: true
Casbin:
  Enabled: true
  ModelPath: configs/rbac_model.conf
  PolicyTable: casbin_rule
  AutoLoadInterval: 30
Log:
  Level: debug
  Output: ["console"]
  Format: text
  FileConfig:
    Path: logs/app.log
    MaxSize: 10
  StackTraceEnabled: true
  StackTraceLevel: error
  MaxStackFrames: 10
Storage:
  Enabled: true
  Type: local  # 可选: local, s3
  BasePath: files
  Local:
    RootPath: storage
    PublicURL: http://localhost:8080/static
  S3:
    # MinIO配置示例
    Endpoint: localhost:9000
    Region: us-east-1
    Bucket: my-files  # 使用public/和private/目录区分
    AccessKeyID: minioadmin
    SecretAccessKey: minioadmin
    UseSSL: false
    ForcePathStyle: true  # MinIO需要

    # AWS S3配置示例（注释掉上面的MinIO配置，启用下面的配置）
    # Endpoint: ""  # 留空使用AWS S3默认端点
    # Region: us-east-1
    # Bucket: my-aws-bucket
    # AccessKeyID: "AKIAIOSFODNN7EXAMPLE"
    # SecretAccessKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    # UseSSL: true
    # ForcePathStyle: false  # AWS S3使用虚拟主机样式
Admin:
  Username: admin
  Password: admin123
  Nickname: 系统管理员
I18n:
  Enabled: true
  DefaultLanguage: zh-CN
  SupportLanguages:
    - zh-CN
    - en-US
  ResourcesPath: locales

# 性能分析配置
Pprof:
  Enabled: false  # 是否启用pprof，生产环境建议设为false
  Port: 0         # pprof服务端口，0表示使用主服务端口，也可以设置独立端口如6060