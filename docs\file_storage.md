# 文件存储架构设计

## 概述

新的文件存储架构采用现代化的设计理念，支持多种存储后端，提供统一的API接口，支持直接上传下载和公开访问，避免服务代理，提高性能和用户体验。

## 核心特性

### 🚀 **统一接口**
- 支持本地存储、MinIO、AWS S3、阿里云OSS等多种存储后端
- 统一的BlobStore接口，轻松切换存储类型
- 配置驱动，无需修改代码

### 🔒 **安全访问**
- 支持公开和私有文件分离存储
- 预签名URL机制，安全可控的临时访问
- 权限检查，确保用户只能访问自己的文件

### ⚡ **高性能**
- 直接上传下载，避免服务代理
- 公开文件CDN友好，支持直接访问
- 预签名URL减少服务器负载

### 🎯 **简洁设计**
- 清晰的API设计，易于理解和使用
- 最小化的代码复杂度
- 良好的错误处理和日志记录

## 架构组件

### 1. BlobStore接口层
```go
type BlobStore interface {
    // 基础操作
    Put(ctx context.Context, key string, data io.Reader, opts *PutOptions) error
    Get(ctx context.Context, key string) (io.ReadCloser, error)
    Delete(ctx context.Context, key string) error
    
    // 预签名URL
    GetPresignedUploadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error)
    GetPresignedDownloadURL(ctx context.Context, key string, opts *PresignedOptions) (*PresignedURL, error)
    
    // 公开访问
    GetPublicURL(key string) string
}
```

### 2. 存储实现
- **LocalStore**: 本地文件系统存储
- **MinIOStore**: MinIO对象存储（兼容S3）
- **S3Store**: AWS S3存储（复用MinIO实现）

### 3. 服务层
- **FileService**: 文件业务逻辑处理
- 文件上传、下载、删除等核心功能
- 权限检查和状态管理

### 4. API层
- **FileHandlerV2**: 简化的文件API处理器
- RESTful API设计
- 统一的错误处理和响应格式

## 使用流程

### 文件上传流程

#### 1. 获取上传URL
```http
POST /api/v1/files/upload-url
{
    "filename": "document.pdf",
    "content_type": "application/pdf",
    "file_type": "document",
    "is_public": false,
    "usage": "attach"
}
```

响应：
```json
{
    "file_id": 123,
    "key": "files/document/2025/06/17/1001_1734567890_document.pdf",
    "upload_url": "https://minio.example.com/private/files/document/...",
    "expires_at": 1734568790
}
```

#### 2. 直接上传到存储
客户端使用返回的`upload_url`直接上传文件到存储服务，无需通过应用服务器代理。

#### 3. 确认上传
```http
POST /api/v1/files/123/confirm
```

### 文件下载流程

#### 公开文件
直接访问公开URL，无需认证：
```
https://minio.example.com/public/files/image/2025/06/17/avatar.jpg
```

#### 私有文件
1. 获取下载URL：
```http
GET /api/v1/files/123/download-url
```

2. 使用返回的预签名URL下载文件

## 配置说明

### 本地存储配置
```yaml
Storage:
  Type: local
  Local:
    RootPath: storage          # 存储根目录
    PublicURL: /static         # 公开文件URL前缀
    ServeMode: direct          # direct: 直接访问, proxy: 代理访问
```

### MinIO配置
```yaml
Storage:
  Type: minio
  MinIO:
    Endpoint: localhost:9000
    AccessKeyID: minioadmin
    SecretAccessKey: minioadmin
    UseSSL: false
    Bucket: private-files      # 私有文件桶
    PublicBucket: public-files # 公开文件桶
```

### S3配置
```yaml
Storage:
  Type: s3
  S3:
    Region: us-east-1
    Bucket: my-private-bucket
    PublicBucket: my-public-bucket
    AccessKeyID: AKIAIOSFODNN7EXAMPLE
    SecretAccessKey: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    UseSSL: true
```

## 文件组织结构

### 存储路径规则
```
{BasePath}/{FileType}/{Year}/{Month}/{Day}/{UserID}_{Timestamp}_{OriginalName}
```

示例：
```
files/image/2025/06/17/1001_1734567890_avatar.jpg
files/document/2025/06/17/1002_1734567891_report.pdf
```

### 公开vs私有文件
- **公开文件**: 存储在public bucket，可直接访问
- **私有文件**: 存储在private bucket，需要预签名URL访问

## API接口

### 文件上传
- `POST /api/v1/files/upload-url` - 获取上传URL
- `POST /api/v1/files/:id/upload` - 直接上传（仅本地存储）
- `POST /api/v1/files/:id/confirm` - 确认上传完成

### 文件下载
- `GET /api/v1/files/:id/download-url` - 获取下载URL
- `GET /api/v1/files/:id/download` - 直接下载（仅本地存储）

### 文件管理
- `GET /api/v1/files` - 列出文件
- `GET /api/v1/files/:id` - 获取文件信息
- `DELETE /api/v1/files/:id` - 删除文件

## 优势对比

### 旧架构问题
- ❌ 复杂的代理上传下载逻辑
- ❌ 服务器带宽和性能瓶颈
- ❌ 代码复杂，难以维护
- ❌ 不支持CDN优化

### 新架构优势
- ✅ 直接上传下载，高性能
- ✅ 统一接口，易于扩展
- ✅ 公开文件CDN友好
- ✅ 代码简洁，易于维护
- ✅ 安全的权限控制
- ✅ 支持多种存储后端

## 迁移指南

1. **配置更新**: 更新存储配置为新格式
2. **数据库迁移**: 更新文件表结构（添加is_public字段等）
3. **API更新**: 使用新的文件API接口
4. **客户端更新**: 适配新的上传下载流程

## 最佳实践

1. **文件类型验证**: 在客户端和服务端都进行文件类型检查
2. **大小限制**: 根据文件类型设置合理的大小限制
3. **权限控制**: 严格检查文件访问权限
4. **错误处理**: 提供清晰的错误信息和处理建议
5. **监控日志**: 记录关键操作日志，便于问题排查
