package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/limitcool/starter/internal/api/response"
	v1 "github.com/limitcool/starter/internal/api/v1"
	"github.com/limitcool/starter/internal/pkg/errorx"
	"github.com/limitcool/starter/internal/service"
)

// FileHandler 文件处理器（基于Blob存储的新架构）
type FileHandler struct {
	*BaseHandler
	fileService *service.FileService
}

// NewFileHandler 创建文件处理器
func NewFileHandler(baseHandler *BaseHandler, fileService *service.FileService) *FileHandler {
	handler := &FileHandler{
		BaseHandler: baseHandler,
		fileService: fileService,
	}

	handler.LogInit("FileHandler")
	return handler
}

// GetUploadURL 获取上传URL
func (h *FileHandler) GetUploadURL(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 绑定请求参数
	var req v1.FileUploadURLRequest
	if !h.Helper.BindJSON(ctx, &req, "GetUploadURL") {
		return
	}

	// 转换为服务层请求
	serviceReq := &service.UploadRequest{
		Filename:    req.Filename,
		ContentType: req.ContentType,
		FileType:    req.FileType,
		IsPublic:    req.IsPublic,
		Usage:       req.Usage,
	}

	// 调用服务层
	result, err := h.fileService.GetUploadURL(ctx.Request.Context(), userID, serviceReq)
	if err != nil {
		h.Helper.HandleDBError(ctx, err, "GetUploadURL", "user_id", userID)
		return
	}

	h.Helper.LogSuccess(ctx, "GetUploadURL", "user_id", userID, "file_id", result.FileID)
	response.Success(ctx, result)
}

// UploadFile 直接上传文件（仅用于本地存储）
func (h *FileHandlerV2) UploadFile(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取文件ID
	fileIDStr := ctx.Param("id")
	fileID, ok := h.Helper.ValidateID(ctx, fileIDStr, "UploadFile")
	if !ok {
		return
	}

	// 获取上传的文件
	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		h.Helper.LogWarning(ctx, "UploadFile no file uploaded", "error", err)
		response.Error(ctx, errorx.ErrInvalidParams.WithMsg("请选择要上传的文件"))
		return
	}

	// 打开文件
	file, err := fileHeader.Open()
	if err != nil {
		h.Helper.LogError(ctx, "UploadFile failed to open file", "error", err)
		response.Error(ctx, errorx.ErrInvalidParams.WithMsg("文件打开失败"))
		return
	}
	defer file.Close()

	// 这里需要实现本地存储的直接上传逻辑
	// 由于篇幅限制，这里只是一个占位符
	// 实际实现中需要调用 blobStore.Put() 方法

	h.Helper.LogSuccess(ctx, "UploadFile", "user_id", userID, "file_id", fileID)
	response.SuccessNoData(ctx, "文件上传成功")
}

// ConfirmUpload 确认上传完成
func (h *FileHandlerV2) ConfirmUpload(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取文件ID
	fileIDStr := ctx.Param("id")
	fileID, ok := h.Helper.ValidateID(ctx, fileIDStr, "ConfirmUpload")
	if !ok {
		return
	}

	// 调用服务层确认上传
	if err := h.fileService.ConfirmUpload(ctx.Request.Context(), fileID, userID); err != nil {
		h.Helper.HandleDBError(ctx, err, "ConfirmUpload", "user_id", userID, "file_id", fileID)
		return
	}

	h.Helper.LogSuccess(ctx, "ConfirmUpload", "user_id", userID, "file_id", fileID)
	response.SuccessNoData(ctx, "上传确认成功")
}

// GetDownloadURL 获取下载URL
func (h *FileHandlerV2) GetDownloadURL(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取文件ID
	fileIDStr := ctx.Param("id")
	fileID, ok := h.Helper.ValidateID(ctx, fileIDStr, "GetDownloadURL")
	if !ok {
		return
	}

	// 调用服务层获取下载URL
	downloadURL, err := h.fileService.GetDownloadURL(ctx.Request.Context(), fileID, userID)
	if err != nil {
		h.Helper.HandleDBError(ctx, err, "GetDownloadURL", "user_id", userID, "file_id", fileID)
		return
	}

	h.Helper.LogSuccess(ctx, "GetDownloadURL", "user_id", userID, "file_id", fileID)
	response.Success(ctx, gin.H{
		"download_url": downloadURL,
	})
}

// DownloadFile 下载文件（仅用于本地存储或代理下载）
func (h *FileHandlerV2) DownloadFile(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取文件ID
	fileIDStr := ctx.Param("id")
	fileID, ok := h.Helper.ValidateID(ctx, fileIDStr, "DownloadFile")
	if !ok {
		return
	}

	// 这里需要实现文件下载逻辑
	// 由于篇幅限制，这里只是一个占位符
	// 实际实现中需要从 blobStore 获取文件流并返回给客户端

	h.Helper.LogSuccess(ctx, "DownloadFile", "user_id", userID, "file_id", fileID)
}

// DeleteFile 删除文件
func (h *FileHandlerV2) DeleteFile(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取文件ID
	fileIDStr := ctx.Param("id")
	fileID, ok := h.Helper.ValidateID(ctx, fileIDStr, "DeleteFile")
	if !ok {
		return
	}

	// 调用服务层删除文件
	if err := h.fileService.DeleteFile(ctx.Request.Context(), fileID, userID); err != nil {
		h.Helper.HandleDBError(ctx, err, "DeleteFile", "user_id", userID, "file_id", fileID)
		return
	}

	h.Helper.LogSuccess(ctx, "DeleteFile", "user_id", userID, "file_id", fileID)
	response.SuccessNoData(ctx, "文件删除成功")
}

// ListFiles 列出文件
func (h *FileHandlerV2) ListFiles(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取查询参数
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))
	fileType := ctx.Query("file_type")

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// 调用服务层列出文件
	files, total, err := h.fileService.ListFiles(ctx.Request.Context(), userID, page, pageSize, fileType)
	if err != nil {
		h.Helper.HandleDBError(ctx, err, "ListFiles", "user_id", userID)
		return
	}

	h.Helper.LogSuccess(ctx, "ListFiles", "user_id", userID, "count", len(files))
	response.Success(ctx, gin.H{
		"files": files,
		"pagination": gin.H{
			"page":      page,
			"page_size": pageSize,
			"total":     total,
		},
	})
}

// GetFileInfo 获取文件信息
func (h *FileHandlerV2) GetFileInfo(ctx *gin.Context) {
	// 获取用户ID
	userID, ok := h.Helper.GetUserID(ctx)
	if !ok {
		return
	}

	// 获取文件ID
	fileIDStr := ctx.Param("id")
	fileID, ok := h.Helper.ValidateID(ctx, fileIDStr, "GetFileInfo")
	if !ok {
		return
	}

	// 这里需要实现获取文件信息的逻辑
	// 由于篇幅限制，这里只是一个占位符

	h.Helper.LogSuccess(ctx, "GetFileInfo", "user_id", userID, "file_id", fileID)
	response.Success(ctx, gin.H{
		"file_id": fileID,
		"message": "文件信息获取成功",
	})
}
